import { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import GoogleMap from "./GoogleMap";
import {
    Navigation,
    School,
    Landmark,
    Route,
    Plane,
    Clock,
    Car,
    Building,
} from "lucide-react";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1 },
  },
};
const itemVariants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};


const Location = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [activeTab, setActiveTab] = useState("amenities");

  const nearbyAmenities = [
    { icon: Plane, name: "Kempegowda Int'l Airport", distance: "20 minutes", type: "Airport", description: "Major international airport connectivity", color: "text-blue-500 bg-blue-100" },
    { icon: Route, name: "NH-44 Highway", distance: "5 minutes", type: "Highway", description: "Major highway connectivity to Bengaluru", color: "text-green-500 bg-green-100" },
    { icon: School, name: "Harrow Int'l School", distance: "10 minutes", type: "Education", description: "Premium international school", color: "text-purple-500 bg-purple-100" },
    { icon: Building, name: "Foxconn iPhone Campus", distance: "15 minutes", type: "Employment", description: "₹22,000 Crore manufacturing facility", color: "text-indigo-500 bg-indigo-100" },
    { icon: Landmark, name: "Nandi Hills", distance: "15 minutes", type: "Recreation", description: "Popular hill station and tourist spot", color: "text-teal-500 bg-teal-100" },
    { icon: School, name: "Amity University", distance: "12 minutes", type: "Education", description: "Leading private university", color: "text-orange-500 bg-orange-100	" },
    { icon: Building, name: "SAP Labs Campus", distance: "18 minutes", type: "Employment", description: "Major IT development center", color: "text-sky-500 bg-sky-100" },
    { icon: School, name: "GITAM University", distance: "15 minutes", type: "Education", description: "Renowned educational institution", color: "text-pink-500 bg-pink-100" },
  ];

  const transportation = [
    { icon: Car, name: "Hebbal", time: "25 minutes", method: "Drive" },
    { icon: Plane, name: "Kempegowda Airport", time: "20 minutes", method: "Drive" },
    { icon: Car, name: "Yelahanka", time: "20 minutes", method: "Drive" },
    { icon: Navigation, name: "Metro Phase 2 (Upcoming)", time: "8 minutes", method: "Metro" },
  ];

  const neighborhoods = [
    { name: "Devanahalli", image: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800", description: "North Bengaluru's fastest-growing investment hotspot.", highlights: ["Airport Proximity", "Industrial Growth", "Educational Hub"] },
    { name: "Hebbal", image: "https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg?auto=compress&cs=tinysrgb&w=800", description: "Established locality with excellent infrastructure.", highlights: ["IT Corridor", "Shopping Centers", "Healthcare"] },
    { name: "Yelahanka", image: "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800", description: "Well-developed area with good social infrastructure.", highlights: ["Education", "Parks", "Commercial Centers"] },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "amenities":
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6"
          >
            {nearbyAmenities.map((amenity, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl xs:rounded-2xl p-4 xs:p-6 shadow-card hover:shadow-luxury hover:-translate-y-1 transition-all duration-300 group border border-primary-100/50"
                whileHover={{ y: -2, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-start space-x-3 xs:space-x-4">
                  <div className={`w-10 h-10 xs:w-12 xs:h-12 rounded-full ${amenity.color} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                    <amenity.icon className="h-5 w-5 xs:h-6 xs:w-6" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start mb-1">
                      <h4 className="font-bold text-charcoal-800 text-sm xs:text-base leading-tight pr-2">{amenity.name}</h4>
                      <span className="text-primary-600 text-xs xs:text-sm font-bold flex-shrink-0">{amenity.distance}</span>
                    </div>
                    <p className="text-xs text-charcoal-500 font-semibold mb-2">{amenity.type}</p>
                    <p className="text-charcoal-600 text-xs xs:text-sm leading-relaxed">{amenity.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        );
      case "transport":
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 xs:gap-4 sm:gap-6"
          >
            {transportation.map((transport, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-xl xs:rounded-2xl p-4 xs:p-6 text-center shadow-card hover:shadow-luxury transition-all duration-300 border border-primary-100/50"
                whileHover={{ y: -3, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="w-12 h-12 xs:w-16 xs:h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3 xs:mb-4">
                  <transport.icon className="h-6 w-6 xs:h-8 xs:w-8 text-primary-600" />
                </div>
                <h4 className="font-bold text-charcoal-800 text-sm xs:text-base lg:text-lg mb-1 leading-tight">{transport.name}</h4>
                <div className="flex items-center justify-center space-x-1 xs:space-x-2 text-charcoal-600">
                  <Clock className="h-3 w-3 xs:h-4 xs:w-4 flex-shrink-0" />
                  <span className="text-xs xs:text-sm font-semibold">{transport.time}</span>
                </div>
                <p className="text-charcoal-500 text-xs mt-1">by {transport.method}</p>
              </motion.div>
            ))}
          </motion.div>
        );
      case "neighborhoods":
        return (
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {neighborhoods.map((neighborhood, index) => (
              <motion.div key={index} className="bg-white rounded-2xl overflow-hidden shadow-xl group" variants={itemVariants} whileHover={{ y: -8 }}>
                <div className="relative h-48 overflow-hidden">
                  <img src={neighborhood.image} alt={neighborhood.name} className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500" loading="lazy" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4">
                    <h3 className="text-xl font-bold text-white">{neighborhood.name}</h3>
                  </div>
                </div>
                <div className="p-6 space-y-4">
                  <p className="text-slate-600">{neighborhood.description}</p>
                  <div>
                    <h4 className="font-semibold text-slate-700 text-sm mb-2">Key Highlights:</h4>
                    <div className="flex flex-wrap gap-2">
                      {neighborhood.highlights.map((highlight, idx) => (
                        <span key={idx} className="px-3 py-1 bg-orange-100	 text-orange-700 rounded-full text-xs font-semibold">{highlight}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        );
      default:
        return null;
    }
  };

  return (
    <section id="location-details" className="py-16 sm:py-5 bg-gray-100 font-sans">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          <motion.div variants={itemVariants}>
            <GoogleMap />
          </motion.div>
          
          <motion.div variants={itemVariants} className="flex justify-center">
            <div className="flex flex-wrap justify-center gap-1 xs:gap-2 bg-white rounded-2xl p-2 xs:p-3 shadow-luxury max-w-full overflow-hidden">
              {[
                { id: "amenities", label: "Nearby Amenities", shortLabel: "Amenities" },
                { id: "transport", label: "Transportation", shortLabel: "Transport" },
                { id: "neighborhoods", label: "Neighborhoods", shortLabel: "Areas" },
              ].map((tab) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-3 xs:px-4 sm:px-6 py-2 xs:py-2.5 sm:py-3 rounded-xl xs:rounded-full font-semibold transition-all duration-300 text-xs xs:text-sm sm:text-base min-h-[40px] xs:min-h-[44px] flex items-center justify-center whitespace-nowrap ${
                    activeTab === tab.id
                      ? "bg-primary-500 text-white shadow-lg border-2 border-primary-400"
                      : "text-charcoal-600 hover:bg-primary-50 hover:text-primary-600 border-2 border-transparent"
                  }`}
                  whileHover={{ scale: activeTab === tab.id ? 1 : 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="hidden xs:inline">{tab.label}</span>
                  <span className="xs:hidden">{tab.shortLabel}</span>
                </motion.button>
              ))}
            </div>
          </motion.div>
          
          <div className="min-h-[300px]">
            {renderContent()}
          </div>

        </motion.div>
      </div>
    </section>
  );
};

export default Location;