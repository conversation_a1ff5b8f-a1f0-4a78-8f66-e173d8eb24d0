import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
// Assuming GoogleMap is in the same directory
import GoogleMap from "./GoogleMap"; 
import {
    Navigation,
    School,
    Landmark,
    Route as RouteIcon, // Renamed to avoid conflict with a component
    Plane,
    Clock,
    Car,
    Building,
} from "lucide-react";

// --- ANIMATION VARIANTS ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1, delayChildren: 0.2 },
  },
};

const itemVariants = {
  hidden: { y: 40, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};


// --- LOCATION COMPONENT ---
const Location = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.05,
  });

  const [activeTab, setActiveTab] = useState("amenities");

  const nearbyAmenities = [
    { icon: Plane, name: "Kempegowda Int'l Airport", distance: "20 min", type: "Airport", description: "Major international airport connectivity" },
    { icon: RouteIcon, name: "NH-44 Highway", distance: "5 min", type: "Highway", description: "Major highway connectivity to Bengaluru" },
    { icon: School, name: "Harrow Int'l School", distance: "10 min", type: "Education", description: "Premium international school" },
    { icon: Building, name: "Foxconn iPhone Campus", distance: "15 min", type: "Employment", description: "₹22,000 Crore manufacturing facility" },
    { icon: Landmark, name: "Nandi Hills", distance: "15 min", type: "Recreation", description: "Popular hill station and tourist spot" },
    { icon: School, name: "Amity University", distance: "12 min", type: "Education", description: "Leading private university" },
    { icon: Building, name: "SAP Labs Campus", distance: "18 min", type: "Employment", description: "Major IT development center" },
    { icon: School, name: "GITAM University", distance: "15 min", type: "Education", description: "Renowned educational institution" },
  ];

  const transportation = [
    { icon: Car, name: "Hebbal", time: "25 minutes", method: "Drive" },
    { icon: Plane, name: "Kempegowda Airport", time: "20 minutes", method: "Drive" },
    { icon: Car, name: "Yelahanka", time: "20 minutes", method: "Drive" },
    { icon: Navigation, name: "Metro Phase 2 (Upcoming)", time: "8 minutes", method: "Metro" },
  ];

  const neighborhoods = [
    { name: "Devanahalli", image: "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800", description: "North Bengaluru's fastest-growing investment hotspot.", highlights: ["Airport Proximity", "Industrial Growth", "Educational Hub"] },
    { name: "Hebbal", image: "https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg?auto=compress&cs=tinysrgb&w=800", description: "Established locality with excellent infrastructure.", highlights: ["IT Corridor", "Shopping Centers", "Healthcare"] },
    { name: "Yelahanka", image: "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800", description: "Well-developed area with good social infrastructure.", highlights: ["Education", "Parks", "Commercial Centers"] },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case "amenities":
        return (
          <motion.div
            key="amenities"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {nearbyAmenities.map((amenity, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-white/60 backdrop-blur-sm rounded-2xl p-5 shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group border border-slate-200/80"
              >
                <div className="flex items-start space-x-4">
                  <div className="bg-orange-100 text-orange-600 w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <amenity.icon className="h-6 w-6" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start mb-1">
                      <h4 className="font-bold text-slate-800 text-base leading-tight pr-2">{amenity.name}</h4>
                      <span className="text-orange-600 text-sm font-bold flex-shrink-0">{amenity.distance}</span>
                    </div>
                    <p className="text-xs text-slate-500 font-semibold mb-2">{amenity.type}</p>
                    <p className="text-slate-600 text-sm leading-relaxed">{amenity.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        );
      case "transport":
        return (
          <motion.div
            key="transport"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {transportation.map((transport, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 border border-slate-200/80"
              >
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <transport.icon className="h-8 w-8 text-orange-600" />
                </div>
                <h4 className="font-bold text-slate-800 text-lg mb-1 leading-tight">{transport.name}</h4>
                <div className="flex items-center justify-center space-x-2 text-slate-600">
                  <Clock className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm font-semibold">{transport.time}</span>
                </div>
                <p className="text-slate-500 text-xs mt-1">by {transport.method}</p>
              </motion.div>
            ))}
          </motion.div>
        );
      case "neighborhoods":
        return (
          <motion.div key="neighborhoods" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {neighborhoods.map((neighborhood, index) => (
              <motion.div key={index} className="bg-white rounded-2xl overflow-hidden shadow-xl group transition-all duration-300 hover:-translate-y-2" variants={itemVariants}>
                <div className="relative h-48 overflow-hidden">
                  <img src={neighborhood.image} alt={neighborhood.name} className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500" loading="lazy" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4">
                    <h3 className="text-xl font-bold text-white">{neighborhood.name}</h3>
                  </div>
                </div>
                <div className="p-6 space-y-4">
                  <p className="text-slate-600">{neighborhood.description}</p>
                  <div>
                    <h4 className="font-semibold text-slate-700 text-sm mb-2">Key Highlights:</h4>
                    <div className="flex flex-wrap gap-2">
                      {neighborhood.highlights.map((highlight, idx) => (
                        <span key={idx} className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-semibold">{highlight}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        );
      default:
        return null;
    }
  };

  return (
    <section id="location-details" className="py-20 sm:py-24 bg-slate-50 font-sans relative overflow-hidden">
        {/* Background decorative shapes */}
        <div className="absolute top-1/4 left-0 -translate-x-1/2">
            <div className="w-80 h-80 bg-orange-100/50 rounded-full blur-3xl"></div>
        </div>
        <div className="absolute bottom-1/4 right-0 translate-x-1/2">
            <div className="w-96 h-96 bg-amber-100/50 rounded-full blur-3xl"></div>
        </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
            <div className="text-center">
                <motion.h2 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-slate-800 font-sans mb-4">
                Prime Location & Connectivity
                </motion.h2>
                <motion.div variants={itemVariants} className="w-32 sm:w-40 h-1.5 bg-gradient-to-r from-amber-500 to-orange-500 mx-auto mb-6 rounded-full" />
                <motion.p variants={itemVariants} className="text-lg text-slate-600 max-w-3xl mx-auto">
                Strategically positioned in North Bangalore, offering seamless access to key hubs, amenities, and transport links.
                </motion.p>
            </div>

          <motion.div variants={itemVariants}>
            <GoogleMap />
          </motion.div>
          
          <motion.div variants={itemVariants} className="flex justify-center">
            <div className="flex flex-wrap justify-center gap-2 bg-white/70 backdrop-blur-sm rounded-full p-2 shadow-lg border border-slate-200/80">
              {[
                { id: "amenities", label: "Nearby Amenities" },
                { id: "transport", label: "Transportation" },
                { id: "neighborhoods", label: "Neighborhoods" },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 sm:px-6 py-2.5 rounded-full font-semibold transition-all duration-300 text-sm sm:text-base relative ${
                    activeTab === tab.id
                      ? "text-white"
                      : "text-slate-600 hover:bg-orange-50/50 hover:text-orange-700"
                  }`}
                >
                   {activeTab === tab.id && (
                    <motion.div
                      layoutId="active-tab-indicator"
                      className="absolute inset-0 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full z-0"
                      transition={{ type: "spring", stiffness: 300, damping: 30 }}
                    />
                  )}
                  <span className="relative z-10">{tab.label}</span>
                </button>
              ))}
            </div>
          </motion.div>
          
          <div className="min-h-[300px]">
            {renderContent()}
          </div>

        </motion.div>
      </div>
    </section>
  );
};

export default Location;
