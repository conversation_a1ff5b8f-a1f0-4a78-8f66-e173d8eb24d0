import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { 
  TrendingUp, 
  MapPin, 
  Building2, 
  Plane, 
  GraduationCap, 
  Factory,
  Shield,
  Award,
  ChevronRight,
} from "lucide-react";

// --- ANIMATION VARIANTS ---
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1, delayChildren: 0.2 },
  },
};

const itemVariants = {
  hidden: { y: 40, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

// --- WHY INVEST COMPONENT ---
const WhyInvest = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const investmentReasons = [
    { icon: TrendingUp, title: "High Appreciation Potential", description: "North Bangalore is experiencing rapid growth with significant infrastructure development and industrial expansion." },
    { icon: Plane, title: "Airport Proximity", description: "Just 20 minutes from Kempegowda International Airport, ensuring excellent connectivity." },
    { icon: Factory, title: "Industrial Hub", description: "Close to major industrial areas like Aerospace SEZ, Narasapura, and Vemgal." },
    { icon: Building2, title: "IT Corridor", description: "Proximity to AERO SEZ Devanahalli IT Park with companies like SAP Labs, Boeing, and Wipro." },
    { icon: MapPin, title: "Strategic Location", description: "Attached to State Highway 35 with excellent connectivity to Bangalore city and major hubs." },
    { icon: GraduationCap, title: "Educational Infrastructure", description: "Close to Central University of North Bengaluru and other premier educational institutions." },
    { icon: Shield, title: "RERA Approved", description: "Fully RERA and DTCP approved project ensuring legal compliance and buyer protection." },
    { icon: Award, title: "Premium Amenities", description: "World-class facilities including clubhouse, swimming pool, and landscaped gardens." }
  ];

  const developmentHighlights = [
    { title: "Bengaluru Aerospace SEZ", description: "950-acre park with investments from 56 large and mid-sized companies.", impact: "Major Employment Hub" },
    { title: "Devanahalli Business Park", description: "413-acre park expecting $2.2 billion investment over the next 3-5 years.", impact: "Economic Growth Driver" },
    { title: "International Convention Centre", description: "35-acre convention centre next to Bengaluru International Airport.", impact: "Tourism & Business Hub" },
    { title: "Infrastructure Development", description: "Devanahalli-Kolar Road widening from 2-lanes to 6-lanes for higher traffic.", impact: "Enhanced Connectivity" }
  ];

  return (
    <section id="why-invest" className="py-20 sm:py-10 bg-gray-100
 font-sans relative overflow-hidden">
        {/* Background decorative shapes */}
        <div className="absolute top-0 left-0 -translate-x-1/2 -translate-y-1/2">
            <div className="w-80 h-80 bg-orange-100/50 rounded-full blur-3xl"></div>
        </div>
        <div className="absolute bottom-0 right-0 translate-x-1/2 translate-y-1/2">
            <div className="w-96 h-96 bg-amber-100/50 rounded-full blur-3xl"></div>
        </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          <div className="text-center mb-16">
            <motion.h2 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-slate-800 font-sans mb-4">
              Why Invest in Shreyas Sunrise?
            </motion.h2>
            <motion.div variants={itemVariants} className="w-32 sm:w-40 h-1.5 bg-gradient-to-r from-amber-500 to-orange-500 mx-auto mb-6 rounded-full" />
            <motion.p variants={itemVariants} className="text-lg text-slate-600 max-w-3xl mx-auto">
              Discover why Shreyas Sunrise represents one of the most promising investment opportunities in North Bangalore's rapidly developing corridor.
            </motion.p>
          </div>

          {/* Investment Reasons Grid */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-20 items-stretch"
          >
            {investmentReasons.map((reason, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl hover:-translate-y-2 transition-all duration-300 group border border-slate-200/80 flex flex-col"
              >
                <div className="bg-white text-amber-600 w-16 h-16 rounded-xl flex items-center justify-center mb-6 shadow-md group-hover:bg-gradient-to-br group-hover:from-amber-500 group-hover:to-orange-500 group-hover:text-white transition-all duration-300 transform group-hover:scale-110">
                  <reason.icon size={32} strokeWidth={1.5} />
                </div>
                <h3 className="font-bold text-xl text-slate-800 mb-3">
                  {reason.title}
                </h3>
                <p className="text-slate-600 leading-relaxed flex-grow">
                  {reason.description}
                </p>
              </motion.div>
            ))}
          </motion.div>

          {/* Development Highlights */}
          {/* <motion.div
            variants={containerVariants}
            className="bg-white rounded-3xl p-8 lg:p-12 shadow-xl border border-slate-100"
          >
            <motion.h3
              variants={itemVariants}
              className="text-3xl md:text-4xl font-bold text-slate-800 mb-12 text-center"
            >
              Key Developments Driving Growth
            </motion.h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {developmentHighlights.map((highlight, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-slate-50/70 rounded-2xl p-6 border border-slate-200/90 hover:shadow-lg hover:border-slate-300 transition-all duration-300 group"
                >
                    <h4 className="font-bold text-xl text-slate-800 mb-3 group-hover:text-orange-600 transition-colors duration-300">
                      {highlight.title}
                    </h4>
                    <p className="text-slate-600 leading-relaxed mb-4">
                      {highlight.description}
                    </p>
                    <span className="inline-block bg-gradient-to-r from-amber-100 to-orange-100 text-orange-800 text-xs px-3 py-1 rounded-full font-semibold">
                      {highlight.impact}
                    </span>
                </motion.div>
              ))}
            </div>
          </motion.div> */}

          {/* Call to Action */}
          <motion.div
            variants={itemVariants}
            className="text-center mt-20"
          >
            <div className="bg-gradient-to-r from-slate-800 via-slate-900 to-black rounded-2xl p-8 lg:p-12 text-white shadow-2xl">
              <h3 className="text-3xl font-bold mb-4">
                Ready to Invest in Your Future?
              </h3>
              <p className="text-lg mb-8 opacity-80 max-w-2xl mx-auto">
                Don't miss this opportunity to be part of North Bangalore's growth story. Contact us today for site visits and detailed project information.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-8 py-3 rounded-lg font-bold hover:shadow-lg transition-all duration-300 shadow-md flex items-center justify-center gap-2"
                >
                  Book Free Site Visit
                  <ChevronRight size={20}/>
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="border-2 border-slate-600 text-slate-100 px-8 py-3 rounded-lg font-bold hover:bg-slate-100 hover:text-slate-800 transition-all duration-300"
                >
                  Download Brochure
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyInvest;
